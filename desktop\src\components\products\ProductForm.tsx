import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useF<PERSON>, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid,
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  InputAdornment,
  Alert
} from '@mui/material'
import {
  ExpandMore as ExpandMoreIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material'
import { CreateProductForm, UpdateProductForm, ProductUnit, Category } from '@shared/types'
import { useCategoryStore } from '../../store/useCategoryStore'
import { FileUpload } from '../common/FileUpload'

// Validation schema
const productSchema = z.object({
  categoryId: z.string().min(1, '<PERSON><PERSON><PERSON> seçimi zorunludur'),
  code: z.string().min(1, '<PERSON>r<PERSON>n kodu zorunludur').max(50),
  barcode: z.string().optional(),
  name: z.string().min(1, 'Ürün adı zorunludur').max(200),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  basePrice: z.number().min(0, 'Fiyat 0 veya pozitif olmalıdır'),
  taxId: z.string().min(1, 'Vergi oranı seçimi zorunludur'),
  costPrice: z.number().min(0).optional(),
  profitMargin: z.number().min(0).max(100).optional(),
  unit: z.nativeEnum(ProductUnit),
  trackStock: z.boolean(),
  allowNegativeStock: z.boolean(),
  minStockLevel: z.number().min(0).optional(),
  maxStockLevel: z.number().min(0).optional(),
  preparationTime: z.number().min(0).optional(),
  calories: z.number().min(0).optional(),
  allergens: z.array(z.string()),
  tags: z.array(z.string()),
  active: z.boolean(),
  showInMenu: z.boolean(),
  showInKitchen: z.boolean(),
  allowOnlineOrdering: z.boolean(),
  displayOrder: z.number().min(0),
  featured: z.boolean(),
  spicy: z.boolean(),
  vegetarian: z.boolean(),
  vegan: z.boolean(),
  glutenFree: z.boolean(),
  organic: z.boolean()
})

interface ProductFormProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: CreateProductForm | UpdateProductForm) => Promise<void>
  product?: UpdateProductForm | null
  loading?: boolean
}

export const ProductForm: React.FC<ProductFormProps> = ({
  open,
  onClose,
  onSubmit,
  product,
  loading = false
}) => {
  const { t } = useTranslation('products')
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [error, setError] = useState<string | null>(null)

  // Category store
  const { categories, fetchCategories } = useCategoryStore()

  // Form setup
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    watch,
    setValue
  } = useForm<CreateProductForm>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      categoryId: '',
      code: '',
      barcode: '',
      name: '',
      description: '',
      shortDescription: '',
      basePrice: 0,
      taxId: '',
      costPrice: 0,
      profitMargin: 0,
      unit: ProductUnit.PIECE,
      trackStock: true,
      allowNegativeStock: false,
      minStockLevel: 0,
      maxStockLevel: 0,
      preparationTime: 0,
      calories: 0,
      allergens: [],
      tags: [],
      active: true,
      showInMenu: true,
      showInKitchen: true,
      allowOnlineOrdering: true,
      displayOrder: 0,
      featured: false,
      spicy: false,
      vegetarian: false,
      vegan: false,
      glutenFree: false,
      organic: false
    }
  })

  // Load categories on mount
  useEffect(() => {
    if (open) {
      fetchCategories({ active: true })
    }
  }, [open, fetchCategories])

  // Reset form when product changes
  useEffect(() => {
    if (product) {
      reset(product)
    } else {
      reset()
    }
    setUploadedImages([])
    setError(null)
  }, [product, reset])

  // Handle form submission
  const handleFormSubmit = async (data: CreateProductForm) => {
    try {
      setError(null)
      const formData = {
        ...data,
        images: uploadedImages
      }
      await onSubmit(formData)
      onClose()
    } catch (err: any) {
      setError(err.message || 'Bir hata oluştu')
    }
  }

  // Handle file upload
  const handleFileUpload = (files: File[]) => {
    setUploadedImages(files)
  }

  // Mock tax rates (should come from API)
  const taxRates = [
    { id: '1', name: 'KDV %1', rate: 1 },
    { id: '2', name: 'KDV %8', rate: 8 },
    { id: '3', name: 'KDV %18', rate: 18 }
  ]

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {product ? t('editProduct') : t('addProduct')}
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box component="form" sx={{ mt: 1 }}>
          {/* Basic Information */}
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">{t('form.basicInfo')}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('form.name')}
                        fullWidth
                        error={!!errors.name}
                        helperText={errors.name?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="code"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('form.code')}
                        fullWidth
                        error={!!errors.code}
                        helperText={errors.code?.message}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="categoryId"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.categoryId}>
                        <InputLabel>{t('form.category')}</InputLabel>
                        <Select {...field} label={t('form.category')}>
                          {categories.map((category) => (
                            <MenuItem key={category.id} value={category.id}>
                              {category.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="barcode"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('form.barcode')}
                        fullWidth
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('form.description')}
                        fullWidth
                        multiline
                        rows={3}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name="shortDescription"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('form.shortDescription')}
                        fullWidth
                        multiline
                        rows={2}
                      />
                    )}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Pricing */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">{t('form.pricing')}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="basePrice"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('form.basePrice')}
                        type="number"
                        fullWidth
                        InputProps={{
                          startAdornment: <InputAdornment position="start">₺</InputAdornment>
                        }}
                        error={!!errors.basePrice}
                        helperText={errors.basePrice?.message}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="costPrice"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label={t('form.costPrice')}
                        type="number"
                        fullWidth
                        InputProps={{
                          startAdornment: <InputAdornment position="start">₺</InputAdornment>
                        }}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="taxId"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth error={!!errors.taxId}>
                        <InputLabel>{t('products.form.tax')}</InputLabel>
                        <Select {...field} label={t('form.tax')}>
                          {taxRates.map((tax) => (
                            <MenuItem key={tax.id} value={tax.id}>
                              {tax.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="unit"
                    control={control}
                    render={({ field }) => (
                      <FormControl fullWidth>
                        <InputLabel>{t('form.unit')}</InputLabel>
                        <Select {...field} label={t('form.unit')}>
                          {Object.values(ProductUnit).map((unit) => (
                            <MenuItem key={unit} value={unit}>
                              {t(`units.${unit}`)}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Images */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">{t('products.form.images')}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <FileUpload
                onFilesChange={handleFileUpload}
                maxFiles={5}
                acceptedTypes={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}
                helperText={t('products.form.maxImages')}
              />
            </AccordionDetails>
          </Accordion>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          {t('actions.cancel')}
        </Button>
        <Button
          onClick={handleSubmit(handleFormSubmit)}
          variant="contained"
          disabled={loading}
        >
          {t('actions.save')}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
